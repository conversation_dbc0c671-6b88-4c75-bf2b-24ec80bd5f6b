<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body" style="padding: 15px;">
        <form class="layui-form" action="" lay-filter="component-form-group">
        
        
        
        <div class="layui-form-item">
            <label class="layui-form-label">群组名称</label>
            <div class="layui-input-block">
              <input name="title" type="text" class="layui-input" id="title"   placeholder="群组名称" value="{$info.dg_title}">
            </div>
          </div>  
          
          <div class="layui-form-item">
            <label class="layui-form-label">分销比例</label>
            <div class="layui-input-block">
              <input name="count" type="number" class="layui-input" id="count"   placeholder="分销比例" value="{$info.dg_count}">
            </div>
          </div> 
          

          <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
              <input name="content" type="text" class="layui-input" id="content"   placeholder="备注" value="{$info.dg_content}">
            </div>
          </div> 
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
              <input type="radio" name="status" value="1" title="启用" {if $info.dg_status==1}checked=""{/if}>
              <input type="radio" name="status" value="2" title="禁用" {if $info.dg_status==2}checked=""{/if}>
            </div>
          </div>
	  
         <div class="layui-form-item layui-layout-admin">
              <div class="layui-footer" style="left: 0;">
                <div class="layui-btn sub">立即提交</div>
                <button type="reset" class="layui-btn layui-btn-primary ">重置</button>
              </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{include file="common_footer" /} 

<script>
$(".sub").click(function(){
	//if(!$(".btn").hasClass("sub")){return false;}
	var title   = $("#title").val();
	var count   = $("#count").val();
	var content = $("#content").val();
	var status  = $("input[name='status']:checked").val();
 
	if(title==""){
		show_error("群组名称不能为空!");
		return false;
	}
	
	if(count==""){
		show_error("分销比例不能为空!");
		return false;
	}

	$.ajax({
		type:"POST",
		url:"{:url('Distributiongroup/edit')}",
		dataType:"json",
		data:{
			id:"{$info.dg_id}",
			title:title,
			count:count,
			content:content,
			status:status,
		},
		success:function(res){
			if(res.status == 1){
				window.parent.layer.closeAll();//关闭弹窗
				window.parent.location.reload();
				//show_toast_callurl(res.data,"{:url('device/index')}","success");
			}else{
				show_error(res.msg);
			}
		},
		error:function(jqXHR){
			console.log("Error: "+jqXHR.status);
		},
	});
	
});
</script>
</body>
</html>
