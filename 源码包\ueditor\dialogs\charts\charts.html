<!DOCTYPE html>
<html>
    <head>
        <title>chart</title>
        <meta chartset="utf-8">
        <link rel="stylesheet" type="text/css" href="charts.css">
        <script type="text/javascript" src="../internal.js"></script>
    </head>
    <body>
        <div class="main">
            <div class="table-view">
                <h3><var id="lang_data_source"></var></h3>
                <div id="tableContainer" class="table-container"></div>
                <h3><var id="lang_chart_format"></var></h3>
                <form name="data-form">
                    <div class="charts-format">
                        <fieldset>
                            <legend><var id="lang_data_align"></var></legend>
                            <div class="format-item-container">
                                <label>
                                    <input type="radio" class="format-ctrl not-pie-item" name="charts-format" value="1" checked="checked">
                                    <var id="lang_chart_align_same"></var>
                                </label>
                                <label>
                                    <input type="radio" class="format-ctrl not-pie-item" name="charts-format" value="-1">
                                    <var id="lang_chart_align_reverse"></var>
                                </label>
                                <br>
                            </div>
                        </fieldset>
                        <fieldset>
                            <legend><var id="lang_chart_title"></var></legend>
                            <div class="format-item-container">
                                <label>
                                    <var id="lang_chart_main_title"></var><input type="text" name="title" class="data-item">
                                </label>
                                <label>
                                    <var id="lang_chart_sub_title"></var><input type="text" name="sub-title" class="data-item not-pie-item">
                                </label>
                                <label>
                                    <var id="lang_chart_x_title"></var><input type="text" name="x-title" class="data-item not-pie-item">
                                </label>
                                <label>
                                    <var id="lang_chart_y_title"></var><input type="text" name="y-title" class="data-item not-pie-item">
                                </label>
                            </div>
                        </fieldset>
                        <fieldset>
                            <legend><var id="lang_chart_tip"></var></legend>
                            <div class="format-item-container">
                                <label>
                                    <var id="lang_cahrt_tip_prefix"></var>
                                    <input type="text" id="tipInput" name="tip" class="data-item" disabled="disabled">
                                </label>
                                <p><var id="lang_cahrt_tip_description"></var></p>
                            </div>
                        </fieldset>
                        <fieldset>
                            <legend><var id="lang_chart_data_unit"></var></legend>
                            <div class="format-item-container">
                                <label><var id="lang_chart_data_unit_title"></var><input type="text" name="unit" class="data-item"></label>
                                <p><var id="lang_chart_data_unit_description"></var></p>
                            </div>
                        </fieldset>
                    </div>
                </form>
            </div>
            <div class="charts-view">
                <div id="chartsContainer" class="charts-container"></div>
                <div id="chartsType" class="charts-type">
                    <h3><var id="lang_chart_type"></var></h3>
                    <div class="scroll-view">
                        <div class="scroll-container">
                            <div id="scrollBed" class="scroll-bed"></div>
                        </div>
                        <div id="buttonContainer" class="button-container">
                            <a href="#" data-title="prev"><var id="lang_prev_btn"></var></a>
                            <a href="#" data-title="next"><var id="lang_next_btn"></var></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="../../third-party/jquery-1.10.2.min.js"></script>
        <script src="../../third-party/highcharts/highcharts.js"></script>
        <script src="chart.config.js"></script>
        <script src="charts.js"></script>
    </body>
</html>