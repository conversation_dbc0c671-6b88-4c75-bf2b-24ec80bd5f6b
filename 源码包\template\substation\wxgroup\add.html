<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body" style="padding: 15px;">
        <form class="layui-form" action="" lay-filter="component-form-group">
        
        
        
        <div class="layui-form-item">
            <label class="layui-form-label">可提现金额</label>
            <div class="layui-input-block">
              <input name="title" type="text" disabled class="layui-input" id="title" readonly="readonly"   placeholder="可提现金额" value="{$info.du_money}">
            </div>
          </div>  
          
          <div class="layui-form-item">
            <label class="layui-form-label">提现金额</label>
            <div class="layui-input-block">
              <input name="money" type="number" class="layui-input" id="money"   placeholder="提现金额" value="0">
            </div>
          </div> 
          
	  
         <div class="layui-form-item layui-layout-admin">
              <div class="layui-footer" style="left: 0;">
                <div class="layui-btn sub">立即提交</div>
                <button type="reset" class="layui-btn layui-btn-primary ">重置</button>
              </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{include file="common_footer" /} 

<script>
$(".sub").click(function(){
	//if(!$(".btn").hasClass("sub")){return false;}
	var money   = $("#money").val();

	if(money==""){
		show_error("提现金额不能为空!");
		return false;
	}
	
	if(money==0){
		show_error("提现金额不能为0!");
		return false;
	}
	
	if(money > {$info.du_money}){
		show_error("提现金额不能大于可提现金额!");
		return false;
	}

	$.ajax({
		type:"POST",
		url:"{:url('distributiontixian/add')}",
		dataType:"json",
		data:{
			money:money,
		},
		success:function(res){
			if(res.status == 1){
				window.parent.layer.closeAll();//关闭弹窗
				window.parent.location.reload();
				//show_toast_callurl(res.data,"{:url('device/index')}","success");
			}else{
				show_error(res.msg);
			}
		},
		error:function(jqXHR){
			console.log("Error: "+jqXHR.status);
		},
	});
	
});
</script>
</body>
</html>
