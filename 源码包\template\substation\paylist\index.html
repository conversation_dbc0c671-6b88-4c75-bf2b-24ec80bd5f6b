<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
          	<div class="layui-box">
          	 {if !empty($webinfo.text10)}<a class="layui-btn layuiadmin-btn-tags" href="{$webinfo.text10}" target="_blank">功能说明</a>{/if}
			 </div>
			<div class="layui-form" lay-filter="component-form-element">
            <table class="layui-table" lay-even="" lay-skin="nob">
            <thead>
              <tr>
                <th width="35">序号</th>
				<th>名称</th>
                <th>状态</th>
				<th>图标</th>
                <th>备注</th>
				<th width="100">管理</th>
              </tr> 
            </thead>
            <tbody>
             {volist name="list" id="vo"}
				<tr id="tr_{$vo.pl_id}">
					<td class="text-center">{$vo.pl_id}</td>
                    <td><span class="layui-badge-rim">{if $vo.pl_url !=""}<a href="{$vo.pl_url}" target="_blank"><font color="#0033FF" >[点击申请]</font></a>{/if}&nbsp;&nbsp;&nbsp;{$vo.pl_title}</span></td>
                    <td>{if $vo.su_pl_status==1}<span class="layui-badge-dot layui-bg-green"></span>{else}<span class="layui-badge-dot"></span>{/if}</td>
                    <td><img src="{$vo.pl_ico}" width="40" height="40"></td>
                    <td>{$vo.pl_content}</td>
					<td>
							<div class="layui-btn-group">
								<button class="layui-btn layui-btn-sm" data-type="edit" data-id="{$vo.pl_id}"><i class="layui-icon">&#xe642;</i></button>
							</div>
					</td>
				</tr>
			{/volist}
            </tbody>
          </table> 
		  </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {include file="common_footer" /} 
   <script>
	layui.use(['index','form'], function(){
		
		var form = layui.form;
		


    var $ = layui.$, active = {
   
	 
	  edit: function(){
		var id = $(this).data('id');
		var url = '{:url('paylist/edit',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '配置支付'
          ,content: url
          ,area: ['660px', '660px']
        }); 
      },
	 
	  

    } 
	
	 
    $('.layui-btn').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });

	
  });
  </script>
</body>
</html>