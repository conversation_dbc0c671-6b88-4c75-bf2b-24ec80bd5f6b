这次应客户要求，二次开发了首页样式，增加了自动定位，输出用户所在市的功能。具体自己搭建看吧

需要两个域名-分站管理里面需要添加一个分站域名

测试环境：

Nginx 1.20.2+PHP5.6+MYSQL5.7

上传源码和导入数据库

数据库修改：
根目录：/config/database.php

修改ip.php文件ip为自己的主域名：/config/extra/ip.php 


设置伪静态TP
location ~* (runtime|application)/{
	return 403;
}
location / {
	if (!-e $request_filename){
		rewrite  ^(.*)$  /index.php?s=$1  last;   break;
	}
}


后台账号18888888888 密码123456


关于支付：
修改文件  根目录/extend/paylist/payoreo 里面两个文件都修改
在网站后台，支付设置里改，在分站支付里也改一下即可

关于使用：登录后台，点击分站，后面的登录，点击分销会员，登录，点击内容管理，提链，
就是群首页地址，点击编辑，展示广告，广告二维码就是付款输出的群二维码