<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
          	<div class="layui-box">
			<button class="layui-btn layuiadmin-btn-tags" data-type="add">添加</button>
			{if !empty($webinfo.text4)}<a class="layui-btn layuiadmin-btn-tags" href="{$webinfo.text4}" target="_blank">功能说明</a>{/if}
			 </div>
			<div class="layui-form" lay-filter="component-form-element">
            <table class="layui-table" lay-even="" lay-skin="nob">
            <thead>
              <tr>
                <th width="35">序号</th>
				<th>群组名称</th>
				<th>状态</th>
                <th>分销比例</th>
                <th>备注</th>
				<th width="100">管理</th>
              </tr> 
            </thead>
            <tbody>
             {volist name="list" id="vo"}
				<tr id="tr_{$vo.dg_id}">
					<td class="text-center">{$vo.dg_id}</td>
                    <td><span class="layui-badge-rim">{$vo.dg_title}</span></td>
                    <td>{if $vo.dg_status==1}<span class="layui-badge-dot layui-bg-green"></span>{else}<span class="layui-badge-dot"></span>{/if}</td>
                    <td><span class="layui-badge-rim">{$vo.dg_count} %</span></td>
                    <td>{$vo.dg_content}</td>
					<td>
							<div class="layui-btn-group">
								<button class="layui-btn layui-btn-sm" onClick="calldel('{:url('distributiongroup/del',array('id'=>$vo.dg_id))}','tr_{$vo.dg_id}')"><i class="layui-icon">&#xe640;</i></button>
								<button class="layui-btn layui-btn-sm" data-type="edit" data-id="{$vo.dg_id}"><i class="layui-icon">&#xe642;</i></button>
							</div>
					</td>
				</tr>
			{/volist}
            </tbody>
          </table> 
		  </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {include file="common_footer" /} 
   <script>
	layui.use(['index','form'], function(){
		
		var form = layui.form;
		


    var $ = layui.$, active = {
   
	 
	  add: function(){
        layer.open({
          type: 2
          ,title: '添加分销群组'
          ,content: '{:url('distributiongroup/add')}'
          ,area: ['550px', '400px']
        }); 
      },
	 
	 
	  edit: function(){
		var id = $(this).data('id');
		var url = '{:url('distributiongroup/edit',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '修改分销群组'
          ,content: url
          ,area: ['550px', '400px']
        }); 
      },
	 
	  

    } 
	
	 
    $('.layui-btn').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });

	
  });
  </script>
</body>
</html>