<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
          	<div class="layui-box">
			{if($__APS__.add)}<button class="layui-btn layuiadmin-btn-tags" data-type="add">添加</button>{/if}
			 </div>
			<div class="layui-box layui-laypage layui-laypage-molv">{$page}</div>
			<div class="layui-form" lay-filter="component-form-element">
            <table class="layui-table" lay-even="" lay-skin="nob">
            <thead>
              <tr>
				<th width="20"></th>
                <th width="35">序号</th>
				{if($__APS__.supermanage)}<th width="85">超管</th>{/if}
				{if($__APS__.status)}<th width="70">状态</th>{/if}
				<th width="110">手机号</th>
				<th width="60">登录数</th>
				<th>上次登录时间</th>
				<th>上次登录IP</th>
				<th>本次登录时间</th>
				<th>本次登录IP</th>
				{if($__APS__.del or $__APS__.edit)}<th width="100">管理</th>{/if}
              </tr> 
            </thead>
            <tbody>
             {volist name="list" id="vo"}
				<tr id="tr_{$vo.u_id}">
					<td><!--<input type="checkbox" name="delmulti" id="checkbox" value="{$vo.u_id}">--></td>
					<td class="text-center">{$vo.u_id}</td>
					{if($__APS__.status)}<td class="text-center"><input type="checkbox" lay-skin="switch" lay-filter="supermanage" lay-text="超管|非超管" data-id="{$vo.u_id}" data-status="{$vo.u_supermanage}" {if $vo.u_supermanage==2}checked{/if}></td>{/if}
					{if($__APS__.status)}<td class="text-center"><input type="checkbox" lay-skin="switch" lay-filter="status" lay-text="启用|禁用" data-id="{$vo.u_id}" data-status="{$vo.u_status}"  {if $vo.u_status==1}checked{/if}></td>{/if}
					<td>{$vo.u_phone}</td>
					<td class="text-center">{$vo.u_count}</td>
					<td>{$vo.u_last_time}</td>
					<td>{$vo.u_last_ip}</td>
					<td>{$vo.u_this_time}</td>
					<td>{$vo.u_this_ip}</td>
					{if($__APS__.del or $__APS__.edit)}
					<td>
							<div class="layui-btn-group">
								{if($__APS__.del)}<button class="layui-btn layui-btn-sm" onClick="calldel('{:url('users/del',array('id'=>$vo.u_id))}','tr_{$vo.u_id}')"><i class="layui-icon">&#xe640;</i></button>{/if}
								{if($__APS__.addauth)}<button class="layui-btn layui-btn-sm" data-type="addauth" data-id="{$vo.u_id}"><i class="layui-icon">&#xe624;</i></button>{/if}
							</div>
					</td>
					{/if}
				</tr>
			{/volist}
            </tbody>
          </table> 
		  </div>
		  <div class="layui-box layui-laypage layui-laypage-molv">{$page}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {include file="common_footer" /} 
   <script>
	layui.use(['index','form'], function(){
		
		var form = layui.form;
		
		{if($__APS__.supermanage)}
		form.on('switch(supermanage)', function (data) {
			var status = $(this).data('status');
			var id = $(this).data('id');
			var url = '{:url('users/supermanage',array('id'=>'AAAAAA'))}';
			url = url.replace("AAAAAA",id)
			callstatus(url,status);
        });
		{/if}
		
		{if($__APS__.status)}
		form.on('switch(status)', function (data) {
			var status = $(this).data('status');
			var id = $(this).data('id');
			var url = '{:url('users/status',array('id'=>'AAAAAA'))}';
			url = url.replace("AAAAAA",id)
			callstatus(url,status);
        });
		{/if}


    var $ = layui.$, active = {
      {if($__APS__.add)}
	  add: function(){
        layer.open({
          type: 2
          ,title: '添加用户'
          ,content: '{:url('users/add')}'
          ,area: ['550px', '400px']
        }); 
      },
	  {/if}
	  
	  {if($__APS__.edit)}
	  edit: function(){
		var id = $(this).data('id');
		var url = '{:url('users/edit',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '修改用户'
          ,content: url
          ,area: ['550px', '350px']
        }); 
      },
	  {/if}
	  
	  {if($__APS__.addauth)}
	  addauth: function(){
		var id = $(this).data('id');
		var url = '{:url('users/addauth',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '设置用户群组'
          ,content: url
          ,area: ['656px', '563px']
        }); 
      },
	  {/if}
	  

    } 
	
	 
    $('.layui-btn').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });

	
  });
  </script>
</body>
</html>