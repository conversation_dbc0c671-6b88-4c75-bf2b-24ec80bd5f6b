<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body" style="padding: 15px;">
        <form class="layui-form" action="" lay-filter="component-form-group">
        
        <div class="layui-form-item">
            <label class="layui-form-label">所属群组</label>
            <div class="layui-input-block">
				<select name="dg_id" id="dg_id">
					<option value="0"> === 请选择群组 === </option>
					{volist name="list" id="vo"}
						<option value="{$vo.dg_id}" {if $info.dg_id==$vo.dg_id}selected{/if}>{$vo.dg_title}</option>
					{/volist}
				</select>
            </div>
          </div>  
        
        <div class="layui-form-item">
            <label class="layui-form-label">用户名</label>
            <div class="layui-input-block">
              <input name="name" type="text" class="layui-input" id="name"   placeholder="用户名" value="{$info.du_name}">
            </div>
          </div>  
          

         <div class="layui-form-item">
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
              <input name="pass" type="text" class="layui-input" id="pass"   placeholder="密码" value="{$info.du_pass}">
            </div>
          </div>
          
          <div class="layui-form-item">
            <label class="layui-form-label">真实姓名</label>
            <div class="layui-input-block">
              <input name="smname" type="text" class="layui-input" id="smname"   placeholder="真实姓名"  value="{$info.du_smname}">
            </div>
          </div>
          

          <div class="layui-form-item">
            <label class="layui-form-label">手机号</label>
            <div class="layui-input-block">
              <input name="phone" type="text" class="layui-input" id="phone"   placeholder="手机号"  value="{$info.du_phone}">
            </div>
          </div>
          
          <div class="layui-form-item">
            <label class="layui-form-label">支付宝</label>
            <div class="layui-input-block">
              <input name="zfb" type="text" class="layui-input" id="zfb"   placeholder="支付宝"  value="{$info.du_zfb}">
            </div>
          </div>
          
          <div class="layui-form-item">
            <label class="layui-form-label">微信号</label>
            <div class="layui-input-block">
              <input name="wx" type="text" class="layui-input" id="wx"   placeholder="微信号"  value="{$info.du_wx}">
            </div>
          </div>
          
          <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
              <input type="radio" name="status" value="1" title="启用" {if $info.du_status==1}checked=""{/if}>
              <input type="radio" name="status" value="2" title="禁用" {if $info.du_status==2}checked=""{/if}>
            </div>
          </div>
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">模板权限</label>
            <div class="layui-input-block">
              <input type="radio" name="du_tmp" value="1" title="所有功能" {if $info.du_tmp==1}checked=""{/if}>
              <input type="radio" name="du_tmp" value="2" title="指定模板" {if $info.du_tmp==2}checked=""{/if}>
            </div>
          </div>
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">模板</label>
            <div class="layui-input-block">
                {volist name="tmp" id="vo"}
            <input type="checkbox" name="model" value="{$vo.wxgt_id}" title="{$vo.wxgt_title}" {range name="$vo.wxgt_id" value="$info.du_tmpstr" type="in"}checked{/range}>
                {/volist}
            </div>
          </div>
          
	  
         <div class="layui-form-item layui-layout-admin">
              <div class="layui-footer" style="left: 0;">
                <div class="layui-btn sub">立即提交</div>
                <button type="reset" class="layui-btn layui-btn-primary ">重置</button>
              </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{include file="common_footer" /} 

<script>
$(".sub").click(function(){
	//if(!$(".btn").hasClass("sub")){return false;}
	
	var dg_id   = $("#dg_id").val();
	var name    = $("#name").val();
	var pass    = $("#pass").val();
	var smname  = $("#smname").val();
	var phone   = $("#phone").val();
	var zfb     = $("#zfb").val();
	var wx      = $("#wx").val();
	var status  = $("input[name='status']:checked").val();
    var du_tmp  = $("input[name='du_tmp']:checked").val();
 
	var paylists  = "";
	$.each($('input:checkbox'),function(){
		if(this.checked){
			paylists += $(this).val()+",";
		}
	});
 
 	if(dg_id==0){
		show_error("请选择分销群组!");
		return false;
	}
 
	if(name==""){
		show_error("用户名不能为空!");
		return false;
	}
	
	if(pass==""){
		show_error("密码不能为空!");
		return false;
	}

	$.ajax({
		type:"POST",
		url:"{:url('distribution/edit')}",
		dataType:"json",
		data:{
			id:{$info.du_id},
			dg_id:dg_id,
			name:name,
			pass:pass,
			smname:smname,
			phone:phone,
			zfb:zfb,
			wx:wx,
			status:status,
			du_tmp:du_tmp,
			paylists:paylists,
		},
		success:function(res){
			if(res.status == 1){
				window.parent.layer.closeAll();//关闭弹窗
				window.parent.location.reload();
				//show_toast_callurl(res.data,"{:url('device/index')}","success");
			}else{
				show_error(res.msg);
			}
		},
		error:function(jqXHR){
			console.log("Error: "+jqXHR.status);
		},
	});
	
});
</script>
</body>
</html>
