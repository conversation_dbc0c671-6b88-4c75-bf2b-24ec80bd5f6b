<!doctype html>
<html>
	<head>
	<meta charset="utf-8">
	<title>index</title>
	<script src=""></script>
	<script type="text/javascript" src="https://in.td77.shop/data/js/jquery.js"></script>
	<link rel="stylesheet" href="https://in.td77.shop/data/css/comon0.css">
	</head>
	  <style>
    .center {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 35vh;
    }
        #current-date {
            font-size: 20px;
            color: GhostWhite;
        }
  </style>
	<script type="text/javascript">
	$(document).ready(function(){
		var html=$(".wrap ul").html()
		$(".wrap ul").append(html)
		var ls=$(".wrap li").length/2+1
		i=0
		ref = setInterval(function(){
			i++
			if(i==ls){
				i=1
				$(".wrap ul").css({marginTop:0})
				$(".wrap ul").animate({marginTop:-'.52'*i+'rem'},1000)
			}
	    	$(".wrap ul").animate({marginTop:-'.52'*i+'rem'},1000)
		
		
		},2400);
		
		
		
				var html2=$(".adduser ul").html()
		$(".adduser ul").append(html2)
		var ls2=$(".adduser li").length/2+1
		a=0
		ref = setInterval(function(){
			a++
			if(a==ls2){
				a=1
				$(".adduser ul").css({marginTop:0})
				$(".adduser ul").animate({marginTop:-'.5'*a+'rem'},800)
			}
	    	$(".adduser ul").animate({marginTop:-'.5'*a+'rem'},800)
		
		
		},4300);
		
		
		
	
	
		
		
		
	})
	</script>
	<body >
	   <div id="app">

<div class="loading" >
      <div class="loadbox"> <img src="https://in.td77.shop/data/picture/loading.gif"> 页面加载中... </div>
    </div>
<div class="head">
      <h1>青狐进群系统数据中心企业级</h1>
              <div class="weather">
    <div id="current-date"></div>

    <script>
        function updateCurrentDate() {
            const currentDate = new Date();
            const dateSpan = document.getElementById("current-date");
            dateSpan.textContent = currentDate.toLocaleString();
            const dayOfWeek = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"];
            dateSpan.textContent += ` (${dayOfWeek[currentDate.getDay() - 1]})`;
        }

        updateCurrentDate(); // 初始化日期时间
        setInterval(updateCurrentDate, 1000); // 每隔1秒更新一次日期时间
    </script>
    </div>
<div class="mainbox">
      <ul class="clearfix">
    <li>
          <div class="boxall" style="height: 2.7rem">
        <div class="alltitle">数据统计</div>
			  
			<div class="sycm">
			  <ul class="clearfix">
				<li><h2>{$d_money_count|round=###,2}</h2><span>当天销售额</span></li>
				<li><h2>{$d_bill_count}</h2><span>当天订单量</span></li>
				<li><h2>{$d_user_count}</h2><span>当天新用户</span></li>
				</ul>
				<div style="border-bottom: 1px solid rgba(255,255,255,.1)"></div>
				<ul class="clearfix">
				<li><h2>{$z_money_count|round=###,2}</h2><span>总销售额</span></li>
				<li><h2>{$z_bill_count}</h2><span>总订单量</span></li>
				<li><h2>{$z_user_count}</h2><span>总用户数</span></li>
				</ul>
				
				
			  </div>  
        <div class="boxfoot"></div>
      </div>
          <div class="boxall" style="height: 2.65rem">
        <div class="alltitle">消费占比</div>
        <div class="sy" id="echarts1"></div>
        <div class="sy" id="echarts2"></div>
        <div class="sy" id="echarts3"></div>
        <div class="boxfoot"></div>
      </div>
      
      
      
      
      






  
              <!--   <div class="boxall" style="height: 1.9rem">
        <div class="alltitle">代理数据中心</div>
        <div style="text-align: center;fontSize:18px;color:#ffdf00">代理总人数{{responseData?.userdaili}}位</div>
			  
			<div class="sycm">
			  <ul class="clearfix">
				<li><h2>{{responseData?.orderdailij}}</h2><span>今日新增代理</span></li>
				<li><h2>{{responseData?.orderdailiz}}</h2><span>昨日新增代理</span></li>
				<li><h2>{{responseData?.growthRatedaili}}%</h2><span>环比增长</span></li>
				</ul>
			  </div>  
			  
        <div class="boxfoot"></div>
      </div>-->
      <!--    <div class="boxall" style="height: 3.4rem">-->
      <!--  <div class="alltitle">实时入群记录</div>-->
      <!--  <div class="wrap">-->
      <!--        <ul >-->
      <!--          <li v-for="item in ffuserCountlist" :key="item.id"><p>用户{{item.userid}}购买付费入群--金额{{item.money}}元</p></li>-->
      <!--          <li></li>-->
      <!--    </ul>-->
      <!--      </div>-->
      <!--  <div class="boxfoot"></div>-->
      <!--</div>-->
        </li>
    <li>
          <div class="bar">
        <div class="barbox">
              <ul class="clearfix">
            <li class="pulll_left counter">{$z_money_count|round=###,2}</li>
            <li class="pulll_left counter">{$z_bill_count}</li>
          </ul>
            </div>
        <div class="barbox2">
              <ul class="clearfix">
            <li class="pulll_left">消费总销售额</li>
            <li class="pulll_left">消费总订单量</li>
          </ul>
            </div>
      </div>
          <!-- <div class="bar">
        <div class="barbox">
              <ul class="clearfix">
            <li class="pulll_left counter">{{fftotalAmount}}</li>
            <li class="pulll_left counter">{{ffuserCount}}</li>
          </ul>
            </div>
        <div class="barbox2">
              <ul class="clearfix">
            <li class="pulll_left">付费入群总金额</li>
            <li class="pulll_left">付费入群总笔数</li>
          </ul>
            </div>
      </div>
      -->
          <div class="map">
        <div class="map1"><img src="https://in.td77.shop/data/picture/lbx.png"></div>
        <div class="map2"><img src="https://in.td77.shop/data/picture/jt.png"></div>
        <div class="map3"><img src="https://in.td77.shop/data/picture/map.png"></div>
        <div class="map4" id="map_1"></div>
      </div>
        </li>
    <li>
          <div class="boxall" style="height:4.2rem">
        <div class="alltitle">今日天气预报</div>
        <div class="">
          <!--    <ul class="clearfix">-->
          <!--  <li><a class="active" href="#">7天</a></li>-->
          <!--  <li><a href="#">15天</a></li>-->
          <!--  <li><a href="#">30天</a></li>-->
          <!--</ul>-->
            </div>

        <div class="">
              <div class=""><span></span></div>
              <div class="">
            <ul class="">
                
                     </li>
                     <li></li>

                </ul>
          </div>
            </div>
        <div class="center">
            <div id="he-plugin-standard"></div>
<script>
WIDGET = {
  "CONFIG": {
    "layout": "2",
    "width": 500,
    "height": 300,
    "background": "5",
    "dataColor": "FFFFFF",
    "borderRadius": "10",
    "key": "33672891469945ef87150d6cd3a6d875"
  }
}
</script>
<script src="https://widget.qweather.net/standard/static/js/he-standard-common.js?v=2.0"></script>
        </div>
      </div>
          <div class="boxall" style="height: 3.4rem">
                    <div class="alltitle">实时数据显示</div>
                    <div class="wrap">
                        <ul>
                            <li>
                                <p>💗总销售额💗-------------------〖{$z_money_count|round=###,2}元〗</p>
                            </li>
                            <li>
                                <p>💗当天销售💗-------------------〖{$d_money_count|round=###,2}元〗</p>
                            </li>
                            <li>
                                <p>💗总订单量💗-------------------〖{$z_bill_count}单〗</p>
                            </li>
                            <li>
                                <p>💗当天订单💗-------------------〖{$d_bill_count}单〗</p>
                            </li>
                            <li>
                                <p>💗当天新户💗-------------------〖{$d_user_count}人〗</p>
                            </li>
                            <li>
                                <p>💗总用户数💗-------------------〖{$z_user_count}人〗</p>
                            </li>
                        </ul>
                    </div>
                    <div class="boxfoot"></div>
                </div>
            </li>
        </ul>
 
    </div>

<div class="back"></div>
</div>



















<!DOCTYPE html>
<html>
<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	{include file="common_header" /}
	{include file="common_top" /}
	<style>
	.action{border:1px solid #090; color:#090; font-weight:bolder;}
	</style>
</head>
<body>

  
  

  
  

  
  

  
  
  
<div class="layui-fluid">
  <div class="layui-row layui-col-space15">

	
    <div class="layui-col-md3">
        <!-- 1 -->
        <div class="layui-card">
            
            

            <div class="layui-card-body">
                <table class="layui-table" lay-even="" lay-skin="nob">
                    <thead>
                        <tr>
                            <th>分站名称</th>
                            <th>日收款</th>
                        </tr> 
                    </thead>
                    <tbody>
                        {volist name="bill_day" id="vo"}
                        <tr>
                            <td>{$vo.su_title}</td>
                            <td>{$vo.zj|round=###,2}</td>
                        </tr>
                        {/volist}
                    </tbody>
                </table> 
            </div>
        </div>
        <!-- 1 -->
    </div>


     <div class="layui-col-md3">
        <!-- 1 -->
        <div class="layui-card">

            <div class="layui-card-body">
                <table class="layui-table" lay-even="" lay-skin="nob">
                    <thead>
                        <tr>
                            <th>分站名称</th>
                            <th>周收款</th>
                        </tr> 
                    </thead>
                    <tbody>
                        {volist name="bill_week" id="vo"}
                        <tr>
                            <td>{$vo.su_title}</td>
                            <td>{$vo.zj|round=###,2}</td>
                        </tr>
                        {/volist}
                    </tbody>
                </table> 
            </div>
        </div>
        <!-- 1 -->
    </div>
    
    
    
    <div class="layui-col-md3">
        <!-- 1 -->
        <div class="layui-card">

            <div class="layui-card-body">
                <table class="layui-table" lay-even="" lay-skin="nob">
                    <thead>
                        <tr>
                            <th>分站名称</th>
                            <th>月收款</th>
                        </tr> 
                    </thead>
                    <tbody>
                        {volist name="bill_moon" id="vo"}
                        <tr>
                            <td>{$vo.su_title}</td>
                            <td>{$vo.zj|round=###,2}</td>
                        </tr>
                        {/volist}
                    </tbody>
                </table> 
            </div>
        </div>
        <!-- 1 -->
    </div>
    
    
    
    <div class="layui-col-md3">
        <!-- 1 -->
        <div class="layui-card">

            <div class="layui-card-body">
                <table class="layui-table" lay-even="" lay-skin="nob">
                    <thead>
                        <tr>
                            <th>分站名称</th>
                            <th>总收款</th>
                        </tr> 
                    </thead>
                    <tbody>
                        {volist name="bill_z" id="vo"}
                        <tr>
                            <td>{$vo.su_title}</td>
                            <td>{$vo.zj|round=###,2}</td>
                        </tr>
                        {/volist}
                    </tbody>
                </table> 
            </div>
        </div>
        <!-- 1 -->
    </div>

		
		
  </div>
  </div>












<script language="JavaScript" src="https://in.td77.shop/data/js/js.js"></script> 
<script type="text/javascript" src="https://in.td77.shop/data/js/echarts.min.js"></script> 
<script type="text/javascript" src="https://in.td77.shop/data/js/china.js"></script> 
<script type="text/javascript" src="https://in.td77.shop/data/js/area_echarts.js"></script> 
<script src="https://in.td77.shop/data/"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            myChart1.resize();
            myChart2.resize();
            myChart3.resize();
            myChart6.resize();
            myChart7.resize();
            myChart8.resize();
        })
        window.addEventListener("resize", function () {
            myChart1.resize();
            myChart2.resize();
            myChart3.resize();
            myChart6.resize();
            myChart7.resize();
            myChart8.resize();
        });
    </script>
    <script type="text/javascript">
        var v0 = 1000;
        var v1 = 353;
        var v2 = 178;
        var v3 = 68;



        var myChart6 = echarts.init(document.getElementById('echarts6'));
        option6 = {
            series: [{
                type: 'pie',
                radius: ['70%', '80%'],
                color: '#0088cc',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v1,
                    name: '新增会员',
                    label: {
                        normal: {
                            formatter: v1 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                },
                       {
                           value: v0,
                           name: '老会员',
                           label: {
                               normal: {
                                   formatter: function (params) {
                                       return '占比' + Math.round(v1 / v0 * 100) + '%'
                                   },
                                   textStyle: {
                                       color: '#aaa',
                                       fontSize: 12
                                   }
                               }
                           },
                           itemStyle: {
                               normal: {
                                   color: 'rgba(255,255,255,.2)'
                               },
                               emphasis: {
                                   color: '#fff'
                               }
                           },
                       }]
            }]

        };

        var myChart7 = echarts.init(document.getElementById('echarts7'));
        option7 = {
            series: [{
                type: 'pie',
                radius: ['70%', '80%'],
                color: '#fccb00',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v2,
                    name: '新增领卡会员',
                    label: {
                        normal: {
                            formatter: v2 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                },
                       {
                           value: v0,
                           name: '总领卡会员',
                           label: {
                               normal: {
                                   formatter: function (params) {
                                       return '占比' + Math.round(v2 / v0 * 100) + '%'
                                   },
                                   textStyle: {
                                       color: '#aaa',
                                       fontSize: 12
                                   }
                               }
                           },
                           itemStyle: {
                               normal: {
                                   color: 'rgba(255,255,255,.2)'
                               },
                               emphasis: {
                                   color: '#fff'
                               }
                           },
                       }]
            }]
        };


        var myChart8 = echarts.init(document.getElementById('echarts8'));
        option8 = {


            series: [{

                type: 'pie',
                radius: ['70%', '80%'],
                color: '#62b62f',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v3,
                    name: '女性客户',
                    label: {
                        normal: {
                            formatter: v3 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                }, {
                    value: v0,
                    name: '男性客户',
                    label: {
                        normal: {
                            formatter: function (params) {
                                return '占比' + Math.round(v2 / v0 * 100) + '%'
                            },
                            textStyle: {
                                color: '#aaa',
                                fontSize: 12
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]
        };
        setTimeout(function () {
            myChart6.setOption(option6);
            myChart7.setOption(option7);
            myChart8.setOption(option8);
        }, 500);

    </script>
    <script type="text/javascript">
        var myChart1 = echarts.init(document.getElementById('echarts1'));
        var v1 = 2980;
        var v2 = 5233;
        var v3 = 13.14;
        option1 = {

      
            series: [{
                type: 'pie',
                radius: ['70%', '80%'],
                color: '#0088cc',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v1,
                    name: '平均单客价',
                    label: {
                        normal: {
                            formatter: v3 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                },
                ]
            }]
        };


        var myChart3 = echarts.init(document.getElementById('echarts3'));
        var v1 = 2980 
        var v2 = 5231 
        var v3 = 13.14 
        option2 = {

       
            series: [{
                type: 'pie',
                radius: ['70%', '80%'],
                color: '#fccb00',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v1,
                    name: '男性客户',
                    label: {
                        normal: {
                            formatter: v1 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                }, {
                    value: v2,
                    name: '女性客户',
                    label: {
                        normal: {
                            formatter: function (params) {
                                return '占比' + Math.round(v1 / v3 * 100) + '%'
                            },
                            textStyle: {
                                color: '#aaa',
                                fontSize: 12
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]
        };


        var myChart2 = echarts.init(document.getElementById('echarts2'));
        option3 = {


            series: [{

                type: 'pie',
                radius: ['70%', '80%'],
                color: '#62b62f',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: v2,
                    name: '女性客户',
                    label: {
                        normal: {
                            formatter: v2 + '',
                            textStyle: {
                                fontSize: 20,
                                color: '#fff',
                            }
                        }
                    }
                }, {
                    value: v1,
                    name: '男性客户',
                    label: {
                        normal: {
                            formatter: function (params) {
                                return '占比' + Math.round(v2 / v3 * 100) + '%'
                            },
                            textStyle: {
                                color: '#aaa',
                                fontSize: 12
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]
        };
        setTimeout(function () {
            myChart1.setOption(option1);
            myChart2.setOption(option2);
            myChart3.setOption(option3);
        }, 500);

    </script>



<script type="text/javascript">	
//   $(document).ready(function(){
// 		myChart1.resize();
// 	　 myChart2.resize();
// 		myChart3.resize();  
// 		myChart6.resize();
// 	 　myChart7.resize();
// 		myChart8.resize(); 
// })
// window.addEventListener("resize", function () {
//     	myChart1.resize();
// 	　 myChart2.resize();
// 		myChart3.resize();  
// 		myChart6.resize();
// 	 　myChart7.resize();
// 		myChart8.resize();   
// });	
	</script> 

<script type="text/javascript">
        var myChart = echarts.init(document.getElementById('echarts4'));
       var plantCap = [{
    name: '工业',
    value: '222'
}, {
    name: '农业',
    value: '115'
}, {
    name: '互联网',
    value: '113'
}, {
    name: '医疗',
    value: '95'
}, {
    name: '文学',
    value: '92'
},{
    name: '服装',
    value: '87'
}];
var datalist = [{
    offset: [56, 48],
    symbolSize: 110,
   // opacity: .95,
    color: 'rgba(73,188,247,.14)',
	
}, {

    offset: [30, 70],
    symbolSize: 70,
     color: 'rgba(73,188,247,.14)'
}, {
    offset: [0, 43],
    symbolSize: 60,
    color: 'rgba(73,188,247,.14)'

}, {
    offset: [93, 30],
    symbolSize: 70,
     color: 'rgba(73,188,247,.14)'
}, {
    offset: [26, 19],
    symbolSize: 65,
    color: 'rgba(73,188,247,.14)'
}, {
    offset: [75, 75],
    symbolSize: 60,
     color: 'rgba(73,188,247,.14)'

}];

var datas = [];
for (var i = 0; i < plantCap.length; i++) {
    var item = plantCap[i];
    var itemToStyle = datalist[i];
    datas.push({
        name: item.value + '\n' + item.name,
        value: itemToStyle.offset,
        symbolSize: itemToStyle.symbolSize,
        label: {
            normal: {
                textStyle: {
                    fontSize: 14
                }
            }
        },

        itemStyle: {
            normal: {
               color: itemToStyle.color,
                opacity: itemToStyle.opacity
           }
        },
    })
}
option = {
    grid: {
        show: false,
        top: 10,
        bottom: 10
    },

    xAxis: [{
        gridIndex: 0,
        type: 'value',
        show: false,
        min: 0,
        max: 100,
        nameLocation: 'middle',
        nameGap: 5
    }],

    yAxis: [{
        gridIndex: 0,
        min: 0,
        show: false,
        max: 100,
        nameLocation: 'middle',
        nameGap: 30
    }],
    series: [{
        type: 'scatter',
        symbol: 'circle',
        symbolSize: 120,
        label: {
            normal: {
                show: true,
                formatter: '{b}',
                color: '#FFF',
                textStyle: {
                    fontSize: '30'
                }
            },
        },
        itemStyle: {
            normal: {
                color: '#F30'
            }
        },
        data: datas
    }]

};
 myChart.setOption(option);
	$(document).ready(function(){
	　　myChart.resize();  
	
})
window.addEventListener("resize", function () {
　　myChart.resize();  
});
</script>
<script type="text/javascript" src=""></script>
<script type="text/javascript" src=""></script>
	<script type="text/javascript">
	
	</script>
</body>
</html>