<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">

        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
        <link rel="stylesheet" href="../dist/toast.min.css ">

        <title>Toast</title>
    </head>

    <body>
        <button class="btn-block btn-primary" onclick="show_random_toast();">Show Random Toast</button>
        <br>
        <button class="btn-block btn-primary" onclick="show_random_toast(true);">Pause on Hover</button>
        <br>
        <button class="btn-block btn-primary" onclick="show_image_toast();">Show Image Toast</button>
        <br>
        <button class="btn-block btn-primary" onclick="show_random_snack();">Show Random Snack</button>
        <br>
        <button class="btn-block btn-primary" onclick="show_toast_in_container();">Show Toast in Container</button>

        <div class="mt-5 bg-secondary vh-100" id="my_container">
            <p>My container</p>
        </div>

        <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
        <script src="../dist/toast.min.js"></script>

        <script>
            $('body').tooltip({
                selector: '[data-toggle="tooltip"]'
            });

            const TYPES = ['info', 'warning', 'success', 'error'],
                TITLES = {
                    'info': 'Notice!',
                    'success': 'Awesome!',
                    'warning': 'Watch Out!',
                    'error': 'Doh!'
                },
                CONTENT = {
                    'info': 'Hello, world! This is a toast message.',
                    'success': 'The action has been completed.',
                    'warning': 'It\'s all about to go wrong',
                    'error': 'It all went wrong.'
                };

            function show_random_toast(pause_on_hover = false) {
                let type = TYPES[Math.floor(Math.random() * TYPES.length)],
                    title = TITLES[type],
                    content = CONTENT[type];

                $.toast({
                    title: title,
                    subtitle: '11 mins ago',
                    content: content,
                    type: type,
                    pause_on_hover: pause_on_hover,
                    delay: 5000
                });
            }

            function show_image_toast() {
                let type = TYPES[Math.floor(Math.random() * TYPES.length)],
                    title = TITLES[type],
                    content = CONTENT[type];

                $.toast({
                    title: title,
                    subtitle: '11 mins ago',
                    content: content,
                    type: type,
                    delay: 5000,
                    img: {
                        src: 'https://via.placeholder.com/25',
                        class: 'rounded',
                        title: 'Thumbnail Title',
                        alt: 'Alternative'
                    }
                });
            }

            function show_random_snack() {
                let type = TYPES[Math.floor(Math.random() * TYPES.length)],
                    content = CONTENT[type].replace('toast', 'snack');

                $.toast({
                    title: content,
                    type: type,
                    delay: 5000
                });
            }

            function show_toast_in_container() {
                let type = TYPES[Math.floor(Math.random() * TYPES.length)],
                    title = TITLES[type],
                    content = CONTENT[type];

                $.toast({
                    title: title,
                    subtitle: '11 mins ago',
                    content: content,
                    type: type,
                    delay: 5000,
                    container: $("#my_container")
                });
            }
        </script>
    </body>

</html>
