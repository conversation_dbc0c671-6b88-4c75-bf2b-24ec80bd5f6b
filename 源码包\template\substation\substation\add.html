<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body" style="padding: 15px;">
        <form class="layui-form" action="" lay-filter="component-form-group">
        
        <div class="layui-form-item">
            <label class="layui-form-label">所属群组</label>
            <div class="layui-input-block">
				<select name="su_g_id" id="su_g_id">
					<option value="0"> === 请选择群组 === </option>
					{volist name="Substationgroup" id="vo"}
						<option value="{$vo.su_g_id}">{$vo.su_g_title}</option>
					{/volist}
				</select>
            </div>
          </div> 
        
        <div class="layui-form-item">
            <label class="layui-form-label">分站名称</label>
            <div class="layui-input-block">
              <input name="title" type="text" class="layui-input" id="title"   placeholder="分站名称">
            </div>
          </div>   
  
          
          <div class="layui-form-item">
            <label class="layui-form-label">分站域名</label>
            <div class="layui-input-block">
              <input name="domain" type="text" class="layui-input" id="domain"   placeholder="分站域名">
            </div>
          </div> 
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">分站帐号</label>
            <div class="layui-input-block">
              <input name="username" type="text" class="layui-input" id="username"   placeholder="分站帐号">
            </div>
          </div> 
          
          <div class="layui-form-item">
            <label class="layui-form-label">分站密码</label>
            <div class="layui-input-block">
              <input name="password" type="text" class="layui-input" id="password"   placeholder="分站密码">
            </div>
          </div> 
          
          
          <div class="layui-form-item">
            <label class="layui-form-label">抽拥比</label>
            <div class="layui-input-block">
              <input name="su_dk_cd" type="number" class="layui-input" id="su_dk_cd"   placeholder="抽拥比" value="20">
            </div>
          </div>
          
          
           <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
              <input type="radio" name="status" value="1" title="启用" checked="">
              <input type="radio" name="status" value="2" title="禁用">
              <input type="radio" name="status" value="3" title="到期">
            </div>
          </div>
          
	  
         <div class="layui-form-item layui-layout-admin">
              <div class="layui-footer" style="left: 0;">
                <div class="layui-btn sub">立即提交</div>
                <button type="reset" class="layui-btn layui-btn-primary ">重置</button>
              </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{include file="common_footer" /} 
<script>
$(".sub").click(function(){
	//if(!$(".btn").hasClass("sub")){return false;}
	          
	var su_g_id  = $("#su_g_id").val();
	var title    = $("#title").val();
	var domain   = $("#domain").val();
	var username = $("#username").val();
	var password = $("#password").val();
	var status   = $("input[name='status']:checked").val();
    var su_fzonoff   = $("input[name='su_fzonoff']:checked").val();
    
    
    
    
    var su_dk = $("#su_dk").val();
    var su_dk_cd = $("#su_dk_cd").val();


 
	if(su_g_id==0){
		show_error("请先选择群组!");
		return false;
	}
	
	if(title==""){
		show_error("分站名称不能为空!");
		return false;
	}
	
	if(domain == ""){
		show_error("分站域名不能为空");
		return false;
	}
	
	
	if(username == ""){
		show_error("分站帐号不能为空");
		return false;
	}
	
	if(password == ""){
		show_error("分站密码不能为空");
		return false;
	}
	
	
	$.ajax({
		type:"POST",
		url:"{:url('substation/add')}",
		dataType:"json",
		data:{
			su_g_id:su_g_id,
			title:title,
			domain:domain,
			username:username,
			password:password,
			status:status,
			su_dk:su_dk,
			su_dk_cd:su_dk_cd,
			su_fzonoff:su_fzonoff,
		},
		success:function(res){
			if(res.status == 1){
				window.parent.layer.closeAll();//关闭弹窗
				window.parent.location.reload();
				//show_toast_callurl(res.data,"{:url('device/index')}","success");
			}else{
				show_error(res.msg);
			}
		},
		error:function(jqXHR){
			console.log("Error: "+jqXHR.status);
		},
	});
	
});
</script>
</body>
</html>
