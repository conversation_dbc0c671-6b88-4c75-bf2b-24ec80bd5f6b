<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
          	<div class="layui-box">
			 {if($__APS__.addgroup)}<button class="layui-btn layuiadmin-btn-tags" data-type="addgroup">添加分类</button>{/if}
			 {if($__APS__.add)}<button class="layui-btn layuiadmin-btn-tags" data-type="add">添加权限</button>{/if}
			 <a href="https://www.layui.com/doc/element/icon.html" class="layui-btn layuiadmin-btn-tags" target="_blank">图标大全</a>
			 </div>

			<table class="layui-table" lay-even="" lay-skin="nob">
            <thead>
              <tr>
				<th width="20"></th>
				<th width="35">序号</th>
                <th width="35">排序</th>
				<th width="35">状态</th>
				<th width="230">导航名称</th>
				<th width="230">图标</th>
				<th>路径</th>
				<th></th>
				{if($__APS__.del or $__APS__.edit or $__APS__.delgroup  or $__APS__.editgroup)}<th width="100">管理</th>{/if}
              </tr> 
            </thead>
            <tbody>
             {volist name="list" id="vo"}
				<tr id="tr_{$vo.ns_id}">
					<td><input type="checkbox" name="delmulti" id="checkbox" value="{$vo.ns_id}"></td>
					<td class="text-center">{$vo.ns_id}</td>
					<td class="text-center">{$vo.ns_sort}</td>
					<td class="text-center">{if $vo.ns_status==1}<span class="layui-badge-dot layui-bg-green"></span>{else}<span class="layui-badge-dot"></span>{/if}</td>
					<td><strong><span class="layui-badge layui-bg-cyan">{$vo.ns_title}</span></strong></td>
					<td>{if empty($vo.ns_icon)}<i class="layui-icon layui-icon-auz"></i>{else}<i class="layui-icon {$vo.ns_icon}"></i> {$vo.ns_icon}{/if}</td>
					<td><span class="layui-badge layui-bg-gray">{$vo.ns_controller}/{$vo.ns_method}</span></td>
					<td class="text-center"></td>
					{if($__APS__.del or $__APS__.edit)}
					<td>
							<div class="layui-btn-group">
								{if($__APS__.del)}<button class="layui-btn layui-btn-sm" onClick="calldel('{:url('navigat/del',array('id'=>$vo.ns_id))}','tr_{$vo.ns_id}')"><i class="layui-icon">&#xe640;</i></button>{/if}
								{if($__APS__.edit)}<button class="layui-btn layui-btn-sm" data-type="edit" data-id="{$vo.ns_id}"><i class="layui-icon">&#xe642;</i></button>{/if}
							</div>
					</td>
					{/if}
				</tr>
				{volist name="vo.data" id="vo1"}
				<tr id="tr_{$vo1.ns_id}">
					<td><input type="checkbox" name="delmulti" id="checkbox" value="{$vo1.ns_id}"></td>
					<td class="text-center">{$vo1.ns_id}</td>
					<td class="text-center">{$vo1.ns_sort}</td>
					<td class="text-center">{if $vo1.ns_status==1}<span class="layui-badge-dot layui-bg-green"></span>{else}<span class="layui-badge-dot"></span>{/if}</td>
					<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|_&nbsp;<span class="layui-badge-rim">{$vo1.ns_title}</span></td>
					<td>{if empty($vo1.ns_icon)}<i class="layui-icon layui-icon-auz"></i>{else}<i class="layui-icon {$vo1.ns_icon}"></i> {$vo1.ns_icon}{/if}</td>
					<td><span class="layui-badge layui-bg-gray">{$vo1.ns_controller}/{$vo1.ns_method}</span></td>
					<td class="text-center"></td>
					{if($__APS__.del or $__APS__.edit)}
					<td>
							<div class="layui-btn-group">
								{if($__APS__.del)}<button class="layui-btn layui-btn-sm" onClick="calldel('{:url('navigat/del',array('id'=>$vo1.ns_id))}','tr_{$vo1.ns_id}')"><i class="layui-icon">&#xe640;</i></button>{/if}
								{if($__APS__.edit)}<button class="layui-btn layui-btn-sm" data-type="edit" data-id="{$vo1.ns_id}"><i class="layui-icon">&#xe642;</i></button>{/if}
							</div>
					</td>
					{/if}
				</tr>
				{/volist}
			{/volist}
            </tbody>
          </table> 

          </div>
        </div>
      </div>
    </div>
  </div>
  {include file="common_footer" /} 

<script>
  layui.use(['index'], function(){
    var $ = layui.$, active = {
	 {if($__APS__.add)}
      add: function(){
        layer.open({
          type: 2
          ,title: '添加导航信息'
          ,content: '{:url('navigat/add')}'
          ,area: ['550px', '560px']
        }); 
      },
	  {/if}
	  
	  {if($__APS__.edit)}
	  edit: function(){
		var id = $(this).data('id');
		var url = '{:url('navigat/edit',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '修改导航信息'
          ,content: url
          ,area: ['550px', '560px']
        }); 
      },
	  {/if}

    } 
	
	
    $('.layui-btn').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });

	
  });
  </script>
</body>
</html>