<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
          	<div class="layui-box">
          	    {if $d_info.du_tmp==1}
			<button class="layui-btn layuiadmin-btn-tags" data-type="add">添加群组</button>
			<button class="layui-btn layuiadmin-btn-tags" data-type="addimg">添加单图群组</button>
			{/if}
			{if !empty($webinfo.text11)}<a class="layui-btn layuiadmin-btn-tags" href="{$webinfo.text11}" target="_blank">功能说明</a>{/if}
			 </div>
			<div class="layui-form" lay-filter="component-form-element">
            <div class="layui-box layui-laypage layui-laypage-molv">{$page}</div>
            <table class="layui-table" lay-even="" lay-skin="nob">
            <thead>
              <tr>
                <th width="35">序号</th>
                <th>状态</th>
                <th>群名称</th>
                <th>订单数</th>
                <th>曝光量</th>
                <th>转化率</th>
                <th>入群金额</th>
                <th>总收款</th>
                <th>总赢利</th>
                <th>管理</th>
                <th width="130">管理</th>
              </tr> 
            </thead>
            <tbody>
             {volist name="list" id="vo"}
				<tr id="tr_{$vo.wxg_id}">
					<td class="text-center">{$vo.wxg_id}</td>
                    <td>
                    {if $vo.wxg_status==1}<span class="layui-badge layui-bg-blue">启用</span>
                    {elseif $vo.wxg_status==2}<span class="layui-badge layui-bg-black">禁用</span>{/if}
                    </td>
                    <td><span class="layui-badge-rim">{$vo.wxg_title}</span></td>
                    <td>{$vo.count_ddx}</td>
                    <td>{$vo.wxg_readcount} 次</td>
                    <td>{$vo.count_ddx/ $vo.wxg_readcount * 100|round=###,2}%</td>
                    <td>{$vo.wxg_money} 元</td>
                    <td>{$vo.count_z_money|round=###,2} 元</td>
                    <td>{$vo.count_p_money|round=###,2} 元</td>
                    <td>{$vo.wxg_content}</td>
                    <td>
							<div class="layui-btn-group">
								<button class="layui-btn layui-btn-sm" onClick="calldel('{:url('wxgroup/del',array('id'=>$vo.wxg_id))}','tr_{$vo.wxg_id}')"><i class="layui-icon">&#xe640;</i></button>
								{if $d_info.du_tmp==1}
								{if $vo.wxg_type==1}
                                <button class="layui-btn layui-btn-sm" data-type="edit" data-id="{$vo.wxg_id}"><i class="layui-icon">&#xe642;</i></button>
                                {else}
                                
                                <button class="layui-btn layui-btn-sm" data-type="editimg" data-id="{$vo.wxg_id}"><i class="layui-icon">&#xe642;</i></button>
                                {/if}
                                {/if}
                                <button class="layui-btn layui-btn-sm" data-type="turl" data-id="{$vo.wxg_id}">提链</button>
							</div>
                    </td>
				</tr>
			{/volist}
            </tbody>
          </table> 
          <div class="layui-box layui-laypage layui-laypage-molv">{$page}</div>
		  </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {include file="common_footer" /} 
   <script>
	layui.use(['index','form'], function(){
		
		var form = layui.form;
		


    var $ = layui.$, active = {

	  add: function(){
        layer.open({
          type: 2
          ,title: '添加群组'
          ,content: '{:url('wxgroup/add')}'
          ,area: ['800px', '750px']
        }); 
      },
      
      
	  addimg: function(){
        layer.open({
          type: 2
          ,title: '添加单图群组'
          ,content: '{:url('wxgroup/addimg')}'
          ,area: ['800px', '750px']
        }); 
      },
      
	  
	 
	  edit: function(){
		var id = $(this).data('id');
		var url = '{:url('wxgroup/edit',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '修改群组'
          ,content: url
          ,area: ['800px', '750px']
        }); 
      },
      
      
	  editimg: function(){
		var id = $(this).data('id');
		var url = '{:url('wxgroup/editimg',array('id'=>'AAAAAA'))}';
		url = url.replace("AAAAAA",id)
        layer.open({
          type: 2
          ,title: '修改单图群组'
          ,content: url
          ,area: ['800px', '750px']
        }); 
      },
      
	 
	 
	  turl: function(){
	  
		var id = $(this).data('id');
		var url1 = '{:url('group.php/index/index',array('id'=>'AAAAAA'))}';
		url1 = url1.replace("AAAAAA",id)
		url1 = url1.replace("/fenxiao.php","")
		url1 = "http://{:session("su_domain")}"+url1
		
		//var url = '{:url('wxgroup/turl',array('url'=>'AAAAAA'))}';
		//url = url.replace("AAAAAA",url1)
		url = "https://api.qrserver.com/v1/create-qr-code/?size=290x290&data="+url1
        layer.open({
          type: 2
          ,title: '提链'
          ,content: url
          ,area: ['290px', '336px']
        }); 
	
      }, 
	 
	 
	 
	}

	
	 
    $('.layui-btn').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });

	
  });
  </script>
</body>
</html>