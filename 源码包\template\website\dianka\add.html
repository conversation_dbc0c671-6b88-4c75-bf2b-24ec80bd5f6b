<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body" style="padding: 15px;">
        <form class="layui-form" action="" lay-filter="component-form-group">
        
        
        
        <div class="layui-form-item">
            <label class="layui-form-label">分站帐号</label>
            <div class="layui-input-block">
              <input name="title" type="text" class="layui-input" id="title"   placeholder="分站帐号">
            </div>
          </div>  
          
          <div class="layui-form-item">
            <label class="layui-form-label">充值点数</label>
            <div class="layui-input-block">
              <input name="money" type="number" class="layui-input" id="money"   placeholder="充值点数" >
            </div>
          </div> 
          
	  
         <div class="layui-form-item layui-layout-admin">
              <div class="layui-footer" style="left: 0;">
                <div class="layui-btn sub">立即提交</div>
                <button type="reset" class="layui-btn layui-btn-primary ">重置</button>
              </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{include file="common_footer" /} 
<script>
$(".sub").click(function(){
	//if(!$(".btn").hasClass("sub")){return false;}
	var title   = $("#title").val();
	var money     = $("#money").val();

	if(title==""){
		show_error("分站帐号不能为空!");
		return false;
	}
	
	if(money==""){
		show_error("充值点数不能为空!");
		return false;
	}
	
	$.ajax({
		type:"POST",
		url:"{:url('dianka/add')}",
		dataType:"json",
		data:{
			title:title,
			money:money,
		},
		success:function(res){
			if(res.status == 1){
				window.parent.layer.closeAll();//关闭弹窗
				window.parent.location.reload();
				//show_toast_callurl(res.data,"{:url('device/index')}","success");
			}else{
				show_error(res.msg);
			}
		},
		error:function(jqXHR){
			console.log("Error: "+jqXHR.status);
		},
	});
	
});
</script>
</body>
</html>
