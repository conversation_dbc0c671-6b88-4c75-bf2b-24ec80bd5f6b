<html xmlns="http://www.w3.org/1999/xhtml" style="font-size: 23.8667px;">
<head id="Head1">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>站点提示</title>
    <!--禁止全屏缩放-->
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <!--不显示成手机号-->
    <meta name="format-detection" content="telephone=no">
    <!--删除默认的苹果工具栏和菜单栏-->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!--解决UC手机字体变大的问题-->
    <meta name="wap-font-scale" content="no">
    <!--控制状态栏显示样式-->
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
	<style>
	body, div, form, input, li, ol, p, textarea, ul
{
    margin: 0;
    padding: 0;
}

body
{
    background: #ffffff;
    color: #3f3f3f;
    font-family: Apple LiGothic Medium,SimHei,Geneva,Arial,Helvetica,sans-serif;
    -webkit-tap-highlight-color: transparent;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-appearance: none;
    width: 100%;
    font-size: 16px;
}

a, button, input
{
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-tap-highlight-color: transparent;
    -webkit-user-modify: read-write-plaintext-only;
    -webkit-touch-callout: none;
    -webkit-appearance: none;
    outline: none;
}
a: focus, input: focus
{
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-tap-highlight-color: transparent;
    -webkit-user-modify: read-write-plaintext-only;
    -webkit-touch-callout: none;
    -webkit-appearance: none;
    border: 1px solid #FFFFFF;
    outline: none;
}


ul li
{
    padding: 0px;
    margin: 0px;
}

li
{
    list-style: none;
}

img
{
    border: 0 none;
}
span
{
    padding: 0px;
    margin: 0px;
}




.tx_top
{
    height: 2.8rem;
    margin: 0 auto;
    position: relative;
    background: url(../img/tx_bg.png) no-repeat;
    background-size: 15rem auto;
    overflow: hidden;
}
.top_bg
{
    width: 15rem;
    height: 5.8rem;
    margin: 0 auto;
    position: relative;
    background: url(../img/top_bg.png) no-repeat;
    background-size: 15rem auto;
    overflow: hidden;
}

.srk_bg
{
    width: 15rem;
    height: 7.4rem;
    margin: 0 auto;
    position: relative;
    background: url(../img/srk_bg.png) no-repeat;
    background-size: 15rem auto;
    overflow: hidden;
}
.hdgz_bg
{
    width: 15rem;
    height: 8.28rem;
    margin: 0 auto;
    position: relative;
    background: url(../img/hdgz_bg.png) no-repeat;
    background-size: 15rem auto;
    overflow: hidden;
}

.tx_con
{
    padding-left: 0.8rem;
    padding-top: 0.9rem;
}
.tx_top_tx
{
    width: 1.7rem;
    height: 1.7rem;
    border-radius: 1.6rem;
    border: 0.12rem solid #f6dbdd;
    float: left;
}
.tx_top_tx img
{
    width: 1.7rem;
    height: 1.7rem;
    border-radius: 1.6rem;
}
.tx_top_wz
{
    font-size: 0.52rem;
    color: #FFF;
    line-height: 0.6rem;
    padding-left: 2.8rem;
    padding-right: 0.9rem;
    padding-top: 0.5rem;
}





.wxhd_con_srk
{
    width: 12.4rem;
    margin: 0 auto;
    padding-top: 0.8rem;
}
.wxhd_con_srk_l
{
    background-color: #ffffff;
    border-radius: 0.1rem; height: 1.38rem;
    width: 12.4rem;
}

.wxhd_con_srk_l ul li.srk_nr
{
    float: left;
    width: 12rem;
    padding-left: 0.4rem;
}
.wxhd_con_srk_l ul li.srk_nr input
{ height: 1.38rem;
    width: 12rem;
    border: 0px;
    font-size: 0.6rem;
    text-align: center;
}

.wxhd_con_srk_l ul li.srk_nr2
{
    float: left;
    width: 7rem;
    padding-left: 0.4rem;
}
.wxhd_con_srk_l ul li.srk_nr2 input
{ height: 1.38rem;
    width: 7rem;
    border: 0px;
    font-size: 0.6rem;
}
.wxhd_con_srk_l ul li.srk_nr3
{
    float: right;
    width: 5rem;
    text-align: center;
    line-height: 1.38rem;
    font-size: 0.6rem;
    color: #FFF;
    background-color: #e74129;
    cursor: pointer;
    border-radius: 0rem 0.1rem 0.1rem 0rem;
}
.wxhd_wc
{
    width: 100%;
    padding-top: 1.4rem;
}
.wxhd_wc_an
{
    width: 12.4rem;
    margin: 0 auto;
    text-align: center; height: 1.38rem;
    line-height: 1.38rem;
    font-size: 0.7rem;
    color: #b71f2d;
    border-radius: 0.1rem;
    background-color: #fcc602;
}
.cwts
{
    height: 0.6rem;
    color: #F00;
    font-size: 0.6rem;
    padding-top: 0.4rem;
    text-align: center;
}
.xxtjh
{
    padding-top: 1.5rem;
    text-align: center;
    font-size: 0.6rem;
    padding-bottom: 0.15rem;
    line-height: 1.0rem;
    color: #b91422;
}
.xxtjh span
{
    color: #ff0003;
    font-size: 0.7rem;
}
.wxhd_wc_an2
{
    width:12.4rem;
    margin: 0 auto;
    text-align:center; height: 1.38rem;
    line-height: 1.38rem;
    font-size: 0.7rem;
    color: #fdcb5e;
    border-radius: 0.1rem;
    background-color: #e74129;
}
.fc_jt
{
    width: 15rem;
    height: 4.28rem;
}
.fc_jt img
{
    width: 15rem;
    height: 4.28rem;
}

.fc_wz
{
    height: 6.9rem;
    line-height: 1.0rem;
    font-size: 0.7rem;
    text-align: center;
    color: #333333;
}
.fc_tp
{
    width: 12.44rem;
    height: 10.rem;
    margin: 0 auto;
}
.fc_tp img
{
    width: 12.44rem;
    height: 10.rem;
    text-align: center;
}
.wxhd_wc2
{
    width: 100%;
    padding-top: 0.3rem;
}



.all_main
{
    text-align: center;
    padding-top: 130px;
}
.all_main01
{
    text-align: center;
    padding-top: 30px;
    font-size: 28px;
    color: #999595;
    margin-bottom: 20px;
}
.all_main01 span
{
    color: #9dbad0;
}
.all_main01 a
{
    text-decoration: none;
}

.all_main02
{
    border-radius: 40px;
    -moz-border-radius: 40x; /* Firefox */
    -webkit-border-radius: 40px; /* Safari oí Chrome */
    background-color: #9dbad0;
    width: 400px;
    height: 60px;
    line-height: 60px;
    margin: 0 auto;
    color: #FFF;
    font-size: 24px;
    cursor: pointer;
    text-align: center;
}

.zxyq
{
   
    width: 15rem;
    background-color: #dd3149;
}
.zxyq_title
{
    height: 2.06rem;
    width: 15rem;
    text-align: center;
    background: url(../img/zxyq_title.jpg) center center no-repeat;
    background-size: 5.3rem auto;
}
.zxyq_main
{
    margin: 0 auto;
    width: 12.82rem;
    background-color: #e1485e;
    padding-left: 0.8rem;
    padding-right: 0.8rem;
    padding-top: 0.8rem;
	padding-bottom: 0.8rem;
}

.zxyq_main ul li
{
    clear: both;
    color: #ffffff;
    font-size: 0.56rem;
    line-height: 1.45rem;
	overflow:hidden;
}
.imgtx
{
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    overflow: hidden;
    border: #FFF 0.04rem solid;
    float: left;
    margin-top: 0.2rem;
}
.imgtx img
{
    width: 1rem;
    height: 1rem;
}
.nicheng
{
    width: 2.4rem;
    height: 1rem;
    margin-left: 0.4rem;
    float: left;
}
.mes
{
    width: 5.3rem;
    float: left;
}
.time
{
    width:3rem;
    height: 1rem;
    margin-left: 0.4rem;
    color: #f5adb7;
    float: right;
    text-align: right;

</style>
    <script type="text/javascript" src="//cdn.bootcss.com/jquery/1.12.4/jquery.min.js"></script>
    <script type="text/javascript">
$(function ($) {
    setRootFontSize();
});
window.onresize = function () {
    setRootFontSize();
}
function setRootFontSize() {
    $('html').css('font-size', document.body.clientWidth / 15 + 'px');
}
    </script>
</head>
<body style="background-color: #f5f5f5;">
    <div id="Pan_WX">
        <!--微信访问-->
        <div class="fc_jt">
            <img src="/template/group/index/jt.png"></div>
               <div class="fc_wz">
                点击屏幕右上角[...]<br>
                用 浏览器 打开 
            </div>  
          <div class="fc_tp">
            <img src="/template/group/index/wx_az.png"></div>
		</div>
</body>
</html>