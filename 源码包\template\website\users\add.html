<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>

  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body" style="padding: 15px;">
        <form class="layui-form" action="" lay-filter="component-form-group">
        <div class="layui-form-item">
            <label class="layui-form-label">手机号</label>
            <div class="layui-input-block">
              <input name="phone" type="text" class="layui-input" id="phone"   placeholder="手机号">
            </div>
          </div>    
		  
		  <div class="layui-form-item">
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
              <input name="password" type="text" class="layui-input" id="password"  value="{$password}"   placeholder="密码">
            </div>
          </div> 

		  
          <div class="layui-form-item">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
              <input type="radio" name="status" value="1" title="启用" checked="">
              <input type="radio" name="status" value="2" title="禁用">
            </div>
          </div>   
		<div class="layui-form-item">
            <label class="layui-form-label">超管</label>
            <div class="layui-input-block">
              <input type="radio" name="supermanage" value="1" title="非超管" checked="">
              <input type="radio" name="supermanage" value="2" title="超管">
            </div>
          </div>		  
         <div class="layui-form-item layui-layout-admin">
              <div class="layui-footer" style="left: 0;">
                <div class="layui-btn sub">立即提交</div>
                <button type="reset" class="layui-btn layui-btn-primary ">重置</button>
              </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{include file="common_footer" /} 
<script>
$(".sub").click(function(){
	//if(!$(".btn").hasClass("sub")){return false;}
	var phone       = $("#phone").val();
	var password    = $("#password").val();
	var status      = $("input[name='status']:checked").val();
	var supermanage = $("input[name='supermanage']:checked").val();

	if(phone==""){
		show_error("手机不能为空!");
		return false;
	}
	
	if(password==""){
		show_error("密码不能为空!");
		return false;
	}
	
	$.ajax({
		type:"POST",
		url:"{:url('users/add')}",
		dataType:"json",
		data:{
			phone:phone,
			password:password,
			status:status,
			supermanage:supermanage,
		},
		success:function(res){
			if(res.status == 1){
				window.parent.layer.closeAll();//关闭弹窗
				window.parent.location.reload();
				//show_toast_callurl(res.data,"{:url('device/index')}","success");
			}else{
				show_error(res.msg);
			}
		},
		error:function(jqXHR){
			console.log("Error: "+jqXHR.status);
		},
	});
	
});
</script>
</body>
</html>
