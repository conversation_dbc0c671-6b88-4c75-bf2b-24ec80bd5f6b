/**
 * <AUTHOR> (https://github.com/Script47/Toast)
 * @description Toast - A Bootstrap 4.2+ jQuery plugin for the toast component
 * @version 0.7.1
 **/
 (function(c){c.toast=function(b){var d=c("body");b.container&&1===b.container.length&&(d=b.container);d.children(".toast-container").length||(d.prepend('<div class="toast-container" aria-live="polite" aria-atomic="true"></div>'),d.children(".toast-container").append('<div class="toast-wrapper"></div>'),d.on("hidden.bs.toast",".toast",function(){c(this).remove()}));d=d.children(".toast-container").children(".toast-wrapper");var k="toast-"+(c(".toast").length+1),a="",f=a="",g="text-muted",h="",r=b.title||
 "Notice!",t=b.subtitle||"",q=b.content||"",l=b.delay||-1,e=b.img,m=b.pause_on_hover||!1,n=!1,p="";switch(b.type||"info"){case "info":a="bg-info";h=g=f="text-white";break;case "success":a="bg-success";h=g=f="text-white";break;case "warning":case "warn":a="bg-warning";h=g=f="text-white";break;case "error":case "danger":a="bg-danger",h=g=f="text-white"}!1!==m?(b=Math.floor(Date.now()/1E3)+l/1E3,p='data-autohide="false"',m='data-hide-timestamp="'+b+'"'):p=-1===l?'data-autohide="false"':'data-delay="'+
 l+'"';a='<div id="'+k+'" class="toast" role="alert" aria-live="assertive" aria-atomic="true" '+p+" "+m+">"+('<div class="toast-header '+a+" "+f+'">');"undefined"!==typeof e&&(a+='<img src="'+e.src+'" class="'+(e["class"]||"")+' mr-2" alt="'+(e.alt||"Image")+'" '+("undefined"!==typeof e.title?'data-toggle="tooltip" title="'+e.title+'"':"")+">");a=a+('<strong class="mr-auto">'+r+"</strong>")+('<small class="'+g+'">'+t+"</small>");a+='<button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close">';
 a+='<span aria-hidden="true" class="'+h+'">&times;</span>';a+="</button>";a+="</div>";""!==q&&(a+='<div class="toast-body">',a+=q,a+="</div>");a+="</div>";d.append(a);d.find(".toast:last").toast("show");!1!==m&&(setTimeout(function(){n||c("#"+k).toast("hide")},l),c(document).on("mouseover","#"+k,function(){n=!0}),c(document).on("mouseleave","#"+k,function(){var a=Math.floor(Date.now()/1E3),b=parseInt(c(this).data("hide-timestamp"));n=!1;a>=b&&c(this).toast("hide")}))}})(jQuery);
