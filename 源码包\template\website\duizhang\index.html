<!DOCTYPE html>
<html>
<head>
	{include file="common_header" /}
	{include file="common_top" /}
</head>
<body>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-body">
          	<div class="layui-box">
			{if($__APS__.add)}<button class="layui-btn layuiadmin-btn-tags" data-type="add">添加</button>{/if}
			 </div>
			<div class="layui-box layui-laypage layui-laypage-molv">{$page}</div>
			<div class="layui-form" lay-filter="component-form-element">
            <table class="layui-table" lay-even="" lay-skin="nob">
            <thead>
              <tr>
                <th width="35">序号</th>
				<th>分站</th>
				<th>状态</th>
                <th>金额</th>
				<th>添加时间</th>
				{if($__APS__.del or $__APS__.edit)}<th width="100">管理</th>{/if}
              </tr> 
            </thead>
            <tbody>
             {volist name="list" id="vo"}
				<tr id="tr_{$vo.dz_id}">
					<td class="text-center">{$vo.dz_id}</td>
                    <td><span class="layui-badge-rim">{$vo.su_title}</span></td>
                    <td class="text-center">{if $vo.dz_status==1}未对帐{else}已对帐{/if}</td>
                    <td>{$vo.dz_money}</td>
                    <td>{$vo.dz_date}</td>
					{if($__APS__.del or $__APS__.edit)}
					<td>
							<div class="layui-btn-group">
								{if($__APS__.del)}<button class="layui-btn layui-btn-sm" onClick="calldel('{:url('duizhang/del',array('id'=>$vo.dz_id))}','tr_{$vo.dz_id}')"><i class="layui-icon">&#xe640;</i></button>{/if}
								{if($__APS__.edit)}<button class="layui-btn layui-btn-sm" onClick="calldz('{:url('duizhang/edit',array('id'=>$vo.dz_id))}','tr_{$vo.dz_id}')">对帐</button>{/if}
							</div>
					</td>
					{/if}
				</tr>
			{/volist}
            </tbody>
          </table> 
		  </div>
		  <div class="layui-box layui-laypage layui-laypage-molv">{$page}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {include file="common_footer" /} 
   <script>
	layui.use(['index','form'], function(){
		
		var form = layui.form;
	

    var $ = layui.$, active = {
      {if($__APS__.add)}
	  add: function(){
        layer.open({
          type: 2
          ,title: '添加支付'
          ,content: '{:url('dianka/add')}'
          ,area: ['550px', '300px']
        }); 
      },
	  {/if}

	  

    } 
	
	 
    $('.layui-btn').on('click', function(){
      var type = $(this).data('type');
      active[type] ? active[type].call(this) : '';
    });

	
  });
  </script>
</body>
</html>