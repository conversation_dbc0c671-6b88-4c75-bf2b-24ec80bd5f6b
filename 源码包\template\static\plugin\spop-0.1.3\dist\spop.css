@charset "UTF-8";
/*!
 * smallPop 0.1.2 | https://github.com/silvio-r/spop
 * Copyright (c) 2015 <PERSON><PERSON><PERSON><PERSON> @silvior_
 * MIT license
 */
.spop-container {
  z-index: 2000;
  position: fixed; }
  .spop-container,
  .spop-container *,
  .spop-container *:after,
  .spop-container *:before {
    box-sizing: border-box; }

.spop--top-left {
  top: 0;
  left: 0; }
  .spop--top-left .spop {
    -webkit-transform-origin: 0 0;
        -ms-transform-origin: 0 0;
            transform-origin: 0 0; }

.spop--top-center {
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
      -ms-transform: translateX(-50%);
          transform: translateX(-50%); }
  .spop--top-center .spop {
    -webkit-transform-origin: 50% 0;
        -ms-transform-origin: 50% 0;
            transform-origin: 50% 0; }

.spop--top-right {
  top: 0;
  right: 0; }
  .spop--top-right .spop {
    -webkit-transform-origin: 100% 0;
        -ms-transform-origin: 100% 0;
            transform-origin: 100% 0; }

.spop--center {
  top: 50%;
  left: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0);
          transform: translate3d(-50%, -50%, 0); }
  .spop--center .spop {
    -webkit-transform-origin: 50% 0;
        -ms-transform-origin: 50% 0;
            transform-origin: 50% 0; }

.spop--bottom-left {
  bottom: 0;
  left: 0; }
  .spop--bottom-left .spop {
    -webkit-transform-origin: 0 100%;
        -ms-transform-origin: 0 100%;
            transform-origin: 0 100%; }

.spop--bottom-center {
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
      -ms-transform: translateX(-50%);
          transform: translateX(-50%); }
  .spop--bottom-center .spop {
    -webkit-transform-origin: 50% 100%;
        -ms-transform-origin: 50% 100%;
            transform-origin: 50% 100%; }

.spop--bottom-right {
  bottom: 0;
  right: 0; }
  .spop--bottom-right .spop {
    -webkit-transform-origin: 100% 100%;
        -ms-transform-origin: 100% 100%;
            transform-origin: 100% 100%; }

@media screen and (max-width: 30em) {
  .spop--top-left,
  .spop--top-center,
  .spop--top-right,
  .spop--bottom-left,
  .spop--bottom-center,
  .spop--bottom-right {
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    margin-left: 0;
    -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
            transform: translateX(0); }
    .spop--top-left .spop,
    .spop--top-center .spop,
    .spop--top-right .spop,
    .spop--bottom-left .spop,
    .spop--bottom-center .spop,
    .spop--bottom-right .spop {
      -webkit-transform-origin: 50% 100%;
          -ms-transform-origin: 50% 100%;
              transform-origin: 50% 100%; }
  .spop {
    border-bottom: 1px solid rgba(0, 0, 0, 0.15); } }

.spop {
  position: relative;
  min-height: 56px;
  line-height: 1.25;
  font-size: 14px;
  -webkit-transform: translateZ(0);
          transform: translateZ(0); }
  @media screen and (min-width: 30em) {
    .spop {
      border-radius: 2px;
      width: 320px;
      margin: 0.7em; } }

.spop--info,
.spop--error,
.spop--warning,
.spop--success {
  color: #fff;
  background-color: #454A56; }

@-webkit-keyframes spopIn {
  0% {
    -webkit-transform: scale(0.2, 0.2);
            transform: scale(0.2, 0.2); }
  95% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1); }
  100% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1); } }

@keyframes spopIn {
  0% {
    -webkit-transform: scale(0.2, 0.2);
            transform: scale(0.2, 0.2); }
  95% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1); }
  100% {
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1); } }

@-webkit-keyframes spopOut {
  0% {
    opacity: 1;
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1); }
  20% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1); }
  100% {
    opacity: 0;
    -webkit-transform: scale(0, 0);
            transform: scale(0, 0); } }

@keyframes spopOut {
  0% {
    opacity: 1;
    -webkit-transform: scale(1, 1);
            transform: scale(1, 1); }
  20% {
    -webkit-transform: scale(1.1, 1.1);
            transform: scale(1.1, 1.1); }
  100% {
    opacity: 0;
    -webkit-transform: scale(0, 0);
            transform: scale(0, 0); } }

.spop--out {
  -webkit-animation: spopOut 0.4s ease-in-out;
          animation: spopOut 0.4s ease-in-out; }

.spop--in {
  -webkit-animation: spopIn 0.4s ease-in-out;
          animation: spopIn 0.4s ease-in-out; }

.spop-body {
  padding: 1.4em; }
  .spop-body p {
    margin: 0; }
  .spop-body a {
    color: #fff;
    text-decoration: underline; }
    .spop-body a:hover {
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none; }

.spop-title {
  margin-top: 0;
  margin-bottom: 0.25em;
  color: #fff; }

.spop-close {
  position: absolute;
  right: 0;
  top: 0;
  height: 32px;
  width: 32px;
  padding-top: 7px;
  padding-right: 7px;
  font-size: 22px;
  font-weight: bold;
  text-align: right;
  line-height: 0.6;
  color: #fff;
  opacity: 0.5; }
  .spop-close:hover {
    opacity: 0.7;
    cursor: pointer; }

.spop-icon {
  position: absolute;
  top: 13px;
  left: 16px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  -webkit-animation: spopIn 0.4s 0.4s ease-in-out;
          animation: spopIn 0.4s 0.4s ease-in-out; }
  .spop-icon:after,
  .spop-icon:before {
    content: "";
    position: absolute;
    display: block; }
  .spop-icon + .spop-body {
    padding-left: 4.2em; }

.spop-icon--error,
.spop-icon--info {
  border: 2px solid #3a95ed; }
  .spop-icon--error:before,
  .spop-icon--info:before {
    top: 5px;
    left: 11px;
    width: 4px;
    height: 4px;
    background-color: #3a95ed; }
  .spop-icon--error:after,
  .spop-icon--info:after {
    top: 12px;
    left: 11px;
    width: 4px;
    height: 9px;
    background-color: #3a95ed; }

.spop-icon--error {
  border-color: #ff5656; }
  .spop-icon--error:before {
    top: 16px;
    background-color: #ff5656; }
  .spop-icon--error:after {
    top: 5px;
    background-color: #ff5656; }

.spop-icon--success {
  border: 2px solid #2ecc54; }
  .spop-icon--success:before {
    top: 7px;
    left: 7px;
    width: 13px;
    height: 8px;
    border-bottom: 3px solid #2ecc54;
    border-left: 3px solid #2ecc54;
    -webkit-transform: rotate(-45deg);
        -ms-transform: rotate(-45deg);
            transform: rotate(-45deg); }

.spop-icon--warning {
  border: 2px solid #fcd000; }
  .spop-icon--warning:before {
    top: 7px;
    left: 7px;
    width: 0;
    height: 0;
    border-style: solid;
    border-color: transparent transparent #fcd000 transparent;
    border-width: 0 6px 10px 6px; }
